export enum FACTORY {
    PROXY = "Proxy",

    AGGR_ROUNDS = "AggrRounds",
    AGGR_WIN_BETS_BY_PLAYER = "AggrWinBetsByPlayer",
    AUDIT = "Audit",
    AUDIT_SUMMARY = "AuditSummary",
    AUDIT_SESSION = "AuditSession",
    EVENT_HISTORY = "EventHistory",
    ROUND_HISTORY = "RoundHistory",
    SPIN_HISTORY = "SpinHistory",

    STATIC_DOMAIN = "StaticDomain",
    DYNAMIC_DOMAIN = "DynamicDomain",
    STATIC_DOMAIN_POOL = "StaticDomainPool",
    DYNAMIC_DOMAIN_POOL = "DynamicDomainPool",

    ENTITY = "Entity",
    BRAND = "Brand",
    MERCHANT_ENTITY = "MerchantEntity",

    MERCHANT = "Merchant",

    GAME_PROVIDER = "GameProvider",
    GAME = "Game",
    SLOT_GAME = "SlotGame",
    ACTION_GAME = "ActionGame",
    TABLE_GAME = "TableGame",
    LIVE_GAME = "LiveGame",
    EXTERNAL_GAME = "ExternalGame",

    ENTITY_GAME = "EntityGame",

    JURISDICTION = "Jurisdiction",
    ENTITY_JURISDICTION = "EntityJurisdiction",

    MERCHANT_TEST_PLAYER = "MerchantTestPlayer",
    PLAYER = "Player",

    FREEBET_PROMO = "FreebetPromo",
    Freebet_REWARD = "FreebetReward",

    BONUS_COIN_PROMO = "BonuscoinPromo",
    BONUS_COIN_REWARD = "BonuscoinReward",

    ROLE = "Role",
    USER_ROLE = "UserRole",
    USER = "User",

    LOBBY = "Lobby",

    PAYMENT_ORDER_TRANSFER_IN = "PaymentOrderTransferIn",
    PAYMENT_ORDER_TRANSFER_OUT = "PaymentOrderTransferOut",
    GAME_GROUP = "GameGroup",
    GAME_GROUP_LIMITS = "GameGroupLimits",
    GAME_GROUP_FILTER = "GameGroupFilter",

    AGGR_PLAYER_ROUNDS = "AggrPlayerRounds",

    SCHEMA_DEFINITION = "SchemaDefinitionModel",
    SCHEMA_DEFINITION_LIVE = "SchemaDefinitionLive",
    SCHEMA_CONFIGURATION = "SchemaConfiguration",
    GAME_LIMITS_CONFIGURATION = "GameLimitsConfiguration",
    SEGMENT = "Segment",
    CURRENCY_MULTIPLIER = "CurrencyMultiplier",
    LIMIT_TEMPLATE = "LimitTemplate",

    GAME_CATEGORIES = "GameCategories",

    ENTITY_LABEL = "ENTITY_LABEL",
    LABEL = "LABEL",
    GAME_LABEL = "GAME_LABEL",
    PROMO_LABEL = "PROMO_LABEL",
    LABEL_GROUP = "LABEL_GROUP",
    LIMIT_LEVEL = "LIMIT_LEVEL",
    ENTITY_GAME_LIMIT_LEVEL = "ENTITY_GAME_LIMIT_LEVEL",
    DEPLOYMENT_GROUP_GAME = "DeploymentGroupGame"
    
}
