import { suite, test } from "mocha-typescript";
import { initDB } from "../../skywind/services/user/user";
import { createComplexStructure, truncate } from "../entities/helper";
import { SinonSpy, spy } from "sinon";
import { FACTORY } from "../factories/common";
import { expect } from "chai";
import { BaseEntity } from "../../skywind/entities/entity";
import { GameCategory } from "../../skywind/entities/gamecategory";
import { Game } from "../../skywind/entities/game";
import { GameImpl } from "../../skywind/services/game";
import { getEntityGameService } from "../../skywind/services/entityGameService";
import EntitySettingsService from "../../skywind/services/settings";
import { GameCategoryImpl } from "../../skywind/services/gameCategory/gameCategory";
import {
    gameCategoryCache,
    GameCategoryService,
    getGameCategoryService
} from "../../skywind/services/gameCategory/gameCategoryService";
import {
    gameCategoryGamesCache,
    GameCategoryGamesService
} from "../../skywind/services/gameCategory/gameCategoryGamesService";
import { factory } from "factory-girl";

@suite("Game Categories with games and limits for Lobby")
class GameCategoriesWithGamesAndLimitsSpec {
    public static spyFindAll: SinonSpy = spy(GameCategoryService, "getEntityIdWithCategories");
    public static spyResetEntityGames: SinonSpy;
    public static spyFindCategoryGames: SinonSpy = spy(GameCategoryGamesService, "findAllEntityGames");
    public static master;
    public static brand: BaseEntity;
    public static gameCategories: GameCategory[] = [];
    public static games: Game[] = [];

    public static async before() {
        await truncate();
        GameCategoriesWithGamesAndLimitsSpec.master = await createComplexStructure();
        await initDB();

        GameCategoriesWithGamesAndLimitsSpec.spyResetEntityGames = spy(gameCategoryGamesCache, "reset");

        GameCategoriesWithGamesAndLimitsSpec.brand = await factory.create(FACTORY.BRAND, {}, {
            parent: GameCategoriesWithGamesAndLimitsSpec.master
        });
        GameCategoriesWithGamesAndLimitsSpec.brand.addCurrency("CNY");
        await GameCategoriesWithGamesAndLimitsSpec.brand.save();

        await new EntitySettingsService(GameCategoriesWithGamesAndLimitsSpec.master)
            .patch({ gameGroupsInheritance: true });

        const gameGroup = await factory.create(FACTORY.GAME_GROUP, {},
            { brandId: GameCategoriesWithGamesAndLimitsSpec.master.id, name: "VIP-1" });

        const gamesCodes = [];

        for (let i = 0; i < 10; i++) {
            const code = `sw_game_${i}`;
            gamesCodes.push(code);

            const limits = {
                "USD": {
                    maxTotalStake: 100,
                    stakeAll: [0.1, 0.5, 1, 2, 3, 5],
                    stakeDef: 1,
                    stakeMax: 5,
                    stakeMin: 0.1,
                    winMax: 2000,
                },
                "CNY": {
                    maxTotalStake: 123,
                    stakeAll: [1, 2, 3, 4, 5, 6],
                    stakeDef: 2,
                    stakeMax: 6,
                    stakeMin: 1,
                    winMax: 300,
                },
            };

            const game = await factory.create(FACTORY.GAME, {}, { code, limits });

            GameCategoriesWithGamesAndLimitsSpec.games.push(new GameImpl(game));

            await factory.create(FACTORY.ENTITY_GAME, {}, {
                entityId: GameCategoriesWithGamesAndLimitsSpec.master.id,
                gameId: game.get("id")
            });

            const entityGame = await factory.create(FACTORY.ENTITY_GAME, {}, {
                entityId: GameCategoriesWithGamesAndLimitsSpec.brand.id,
                gameId: game.get("id")
            });

            if (i < 3) {
                await factory.create(FACTORY.GAME_GROUP_LIMITS, {}, {
                    entityGameId: entityGame.id,
                    gameId: entityGame.gameId,
                    gamegroupId: gameGroup.id,
                    limits: {
                        "CNY": {
                            maxTotalStake: 200,
                            stakeAll: [0.2, 0.5, 1.2, 2, 3, 12],
                            stakeDef: 1.2,
                            stakeMax: 12,
                            stakeMin: 0.2,
                            winMax: 20000,
                        }
                    }
                });
            } else {
                await factory.create(FACTORY.GAME_GROUP_LIMITS, {}, {
                    entityGameId: entityGame.id,
                    gameId: entityGame.gameId,
                    gamegroupId: gameGroup.id,
                    limits: {
                        "USD": {
                            maxTotalStake: 3000,
                            stakeAll: [0.3, 0.6, 1.3, 2.6, 3.9, 5, 7],
                            stakeDef: 0.6,
                            stakeMax: 7,
                            stakeMin: 0.3,
                            winMax: 20000,
                        }
                    }
                });
            }

        }

        let categories = await factory.createMany(FACTORY.GAME_CATEGORIES,
            3, {}, {
                brandId: GameCategoriesWithGamesAndLimitsSpec.master.id,
                items: [{
                    "id": "sw_game_0",
                    "type": "game"
                }, {
                    "id": "sw_game_2",
                    "type": "game"
                }, {
                    "id": "sw_game_4",
                    "type": "game"
                }],
                translations: {
                    "en": {
                        title: "Aztak Slot",
                        description: "Aztak slot is …",
                        icon: ""
                    }
                }
            });

        GameCategoriesWithGamesAndLimitsSpec.gameCategories = categories.map(item => new GameCategoryImpl(item));

        categories = await factory.createMany(FACTORY.GAME_CATEGORIES,
            3, {}, {
                brandId: GameCategoriesWithGamesAndLimitsSpec.master.id,
                items: [{
                    "id": "sw_game_3",
                    "type": "game"
                }, {
                    "id": "sw_game_1",
                    "type": "game"
                }, {
                    "id": "sw_game_5",
                    "type": "game"
                }]
            });

        GameCategoriesWithGamesAndLimitsSpec.gameCategories = GameCategoriesWithGamesAndLimitsSpec
            .gameCategories.concat(categories.map(item => new GameCategoryImpl(item)));

        await factory.createMany(FACTORY.GAME_CATEGORIES,
            4, {}, {
                brandId: 2,
                items: [{
                    "id": "sw_game_5",
                    "type": "game"
                }, {
                    "id": "sw_game_7",
                    "type": "game"
                }, {
                    "id": "sw_game_9",
                    "type": "game"
                }]
            });
    }

    public async before() {
        GameCategoriesWithGamesAndLimitsSpec.spyFindAll.resetHistory();
        GameCategoriesWithGamesAndLimitsSpec.spyFindCategoryGames.resetHistory();
        GameCategoriesWithGamesAndLimitsSpec.spyResetEntityGames.resetHistory();
        await gameCategoryCache.reset();
        await gameCategoryGamesCache.reset();
    }

    public static after() {
        GameCategoriesWithGamesAndLimitsSpec.spyFindAll.restore();
        GameCategoriesWithGamesAndLimitsSpec.spyFindCategoryGames.restore();
        GameCategoriesWithGamesAndLimitsSpec.spyResetEntityGames.restore();
    }

    @test()
    public async getGameCategoriesWithoutCache() {
        let found = await getGameCategoryService(GameCategoriesWithGamesAndLimitsSpec.brand)
            .findAllWithGamesForLobby(
                true,
                "ALL");

        expect(found.length).eq(6);
        let filteredCategories = GameCategoriesWithGamesAndLimitsSpec.gameCategories
            .filter(category => category.items.find(item => item.id === "sw_game_3"));

        let expectedCategoryInfo = filteredCategories[0];
        let foundCategoryWithGames = found.find(category => category.id === expectedCategoryInfo.id);

        const game3 = GameCategoriesWithGamesAndLimitsSpec.games.find(game => game.code === "sw_game_3");
        const game1 = GameCategoriesWithGamesAndLimitsSpec.games.find(game => game.code === "sw_game_1");
        const game5 = GameCategoriesWithGamesAndLimitsSpec.games.find(game => game.code === "sw_game_5");

        expect(foundCategoryWithGames).deep.eq({
            "id": expectedCategoryInfo.id,
            "title": expectedCategoryInfo.title,
            "description": expectedCategoryInfo.description,
            "brandId": expectedCategoryInfo.brandId,
            "status": expectedCategoryInfo.status,
            "type": "general",
            "ordering": 0,
            "isEntityOwner": false,
            "icon": expectedCategoryInfo.icon,
            "translations": expectedCategoryInfo.translations,
            "games": [{
                "code": game3.code,
                "title": game3.title,
                "type": game3.type,
                "defaultInfo": game3.defaultInfo,
                "limits": {},
                "providerTitle": foundCategoryWithGames.games[0].providerTitle,
                "features": {}
            }, {
                "code": game1.code,
                "title": game1.title,
                "type": game1.type,
                "defaultInfo": game1.defaultInfo,
                "limits": {},
                "providerTitle": foundCategoryWithGames.games[1].providerTitle,
                "features": {}
            }, {
                "code": game5.code,
                "title": game5.title,
                "type": game5.type,
                "defaultInfo": game5.defaultInfo,
                "limits": {},
                "providerTitle": foundCategoryWithGames.games[2].providerTitle,
                "features": {}
            }]
        });

        expect(GameCategoriesWithGamesAndLimitsSpec.spyFindAll.callCount).eq(1);
        expect(GameCategoriesWithGamesAndLimitsSpec.spyFindCategoryGames.callCount).eq(1);

        found = await getGameCategoryService(GameCategoriesWithGamesAndLimitsSpec.brand)
            .findAllWithGamesForLobby(
                true,
                "USD");

        expect(found.length).eq(6);
        filteredCategories = GameCategoriesWithGamesAndLimitsSpec.gameCategories
            .filter(category => category.items.find(item => item.id === "sw_game_3"));

        expectedCategoryInfo = filteredCategories[0];
        foundCategoryWithGames = found.find(category => category.id === expectedCategoryInfo.id);

        expect(foundCategoryWithGames).deep.eq({
            "id": expectedCategoryInfo.id,
            "title": expectedCategoryInfo.title,
            "description": expectedCategoryInfo.description,
            "brandId": expectedCategoryInfo.brandId,
            "status": expectedCategoryInfo.status,
            "type": "general",
            "ordering": 0,
            "isEntityOwner": false,
            "icon": expectedCategoryInfo.icon,
            "translations": expectedCategoryInfo.translations,
            "games": [{
                "code": game3.code,
                "title": game3.title,
                "type": game3.type,
                "defaultInfo": game3.defaultInfo,
                "limits": { "USD": game3.limits["USD"] },
                "providerTitle": foundCategoryWithGames.games[0].providerTitle,
                "features": {}
            }, {
                "code": game1.code,
                "title": game1.title,
                "type": game1.type,
                "defaultInfo": game1.defaultInfo,
                "limits": { "USD": game1.limits["USD"] },
                "providerTitle": foundCategoryWithGames.games[1].providerTitle,
                "features": {}
            }, {
                "code": game5.code,
                "title": game5.title,
                "type": game5.type,
                "defaultInfo": game5.defaultInfo,
                "limits": { "USD": game5.limits["USD"] },
                "providerTitle": foundCategoryWithGames.games[2].providerTitle,
                "features": {}
            }]
        });

        expect(GameCategoriesWithGamesAndLimitsSpec.spyFindAll.callCount).eq(1);
        expect(GameCategoriesWithGamesAndLimitsSpec.spyFindCategoryGames.callCount).eq(1);
    }

    @test()
    public async getGameCategoriesWithCache() {
        await getGameCategoryService(GameCategoriesWithGamesAndLimitsSpec.brand)
            .findAllWithGamesForLobby(true, "USD");
        const found = await getGameCategoryService(GameCategoriesWithGamesAndLimitsSpec.brand)
            .findAllWithGamesForLobby(true, "USD");

        expect(found.length).eq(6);

        const filteredCategories = GameCategoriesWithGamesAndLimitsSpec.gameCategories
            .filter(category => category.items.find(item => item.id === "sw_game_0"));

        const expectedCategoryInfo = filteredCategories[0];
        const foundCategoryWithGames = found.find(category => category.id === expectedCategoryInfo.id);

        const game0 = GameCategoriesWithGamesAndLimitsSpec.games.find(game => game.code === "sw_game_0");
        const game2 = GameCategoriesWithGamesAndLimitsSpec.games.find(game => game.code === "sw_game_2");
        const game4 = GameCategoriesWithGamesAndLimitsSpec.games.find(game => game.code === "sw_game_4");

        expect(foundCategoryWithGames).deep.eq({
            "id": expectedCategoryInfo.id,
            "title": expectedCategoryInfo.title,
            "description": expectedCategoryInfo.description,
            "brandId": expectedCategoryInfo.brandId,
            "status": expectedCategoryInfo.status,
            "type": "general",
            "ordering": 0,
            "isEntityOwner": false,
            "icon": expectedCategoryInfo.icon,
            "translations": expectedCategoryInfo.translations,
            "games": [{
                "code": game0.code,
                "title": game0.title,
                "type": game0.type,
                "defaultInfo": game0.defaultInfo,
                "limits": { "USD": game0.limits["USD"] },
                "providerTitle": foundCategoryWithGames.games[0].providerTitle,
                "features": {}
            }, {
                "code": game2.code,
                "title": game2.title,
                "type": game2.type,
                "defaultInfo": game2.defaultInfo,
                "limits": { "USD": game2.limits["USD"] },
                "providerTitle": foundCategoryWithGames.games[1].providerTitle,
                "features": {}
            }, {
                "code": game4.code,
                "title": game4.title,
                "type": game4.type,
                "defaultInfo": game4.defaultInfo,
                "limits": { "USD": game4.limits["USD"] },
                "providerTitle": foundCategoryWithGames.games[2].providerTitle,
                "features": {}
            }]
        });

        expect(GameCategoriesWithGamesAndLimitsSpec.spyFindAll.callCount).eq(1);
        expect(GameCategoriesWithGamesAndLimitsSpec.spyFindCategoryGames.callCount).eq(1);
    }

    @test()
    public async addGameGroup() {

        const found = await getGameCategoryService(GameCategoriesWithGamesAndLimitsSpec.brand)
            .findAllWithGamesForLobby(
                true,
                "CNY",
                "VIP-1");

        expect(found.length).eq(6);

        const filteredCategories = GameCategoriesWithGamesAndLimitsSpec.gameCategories
            .filter(category => category.items.find(item => item.id === "sw_game_0"));

        const expectedCategoryInfo = filteredCategories[0];
        const foundCategoryWithGames = found.find(category => category.id === expectedCategoryInfo.id);

        const game0 = GameCategoriesWithGamesAndLimitsSpec.games.find(game => game.code === "sw_game_0");
        const game2 = GameCategoriesWithGamesAndLimitsSpec.games.find(game => game.code === "sw_game_2");
        const game4 = GameCategoriesWithGamesAndLimitsSpec.games.find(game => game.code === "sw_game_4");

        expect(foundCategoryWithGames).deep.eq({
            "id": expectedCategoryInfo.id,
            "title": expectedCategoryInfo.title,
            "description": expectedCategoryInfo.description,
            "brandId": expectedCategoryInfo.brandId,
            "status": expectedCategoryInfo.status,
            "type": "general",
            "ordering": 0,
            "isEntityOwner": false,
            "icon": expectedCategoryInfo.icon,
            "translations": expectedCategoryInfo.translations,
            "games": [{
                "code": game0.code,
                "title": game0.title,
                "type": game0.type,
                "defaultInfo": game0.defaultInfo,
                "limits": {
                    "CNY": {
                        maxTotalStake: 200,
                        stakeAll: [0.2, 0.5, 1.2, 2, 3, 12],
                        stakeDef: 1.2,
                        stakeMax: 12,
                        stakeMin: 0.2,
                        winMax: 20000,
                    }
                },
                "providerTitle": foundCategoryWithGames.games[0].providerTitle,
                "features": {}
            }, {
                "code": game2.code,
                "title": game2.title,
                "type": game2.type,
                "defaultInfo": game2.defaultInfo,
                "limits": {
                    "CNY": {
                        maxTotalStake: 200,
                        stakeAll: [0.2, 0.5, 1.2, 2, 3, 12],
                        stakeDef: 1.2,
                        stakeMax: 12,
                        stakeMin: 0.2,
                        winMax: 20000,
                    }
                },
                "providerTitle": foundCategoryWithGames.games[1].providerTitle,
                "features": {}
            }, {
                "code": game4.code,
                "title": game4.title,
                "type": game4.type,
                "defaultInfo": game4.defaultInfo,
                "limits": { "CNY": game4.limits["CNY"] },
                "providerTitle": foundCategoryWithGames.games[2].providerTitle,
                "features": {}
            }]
        });

    }

    @test()
    public async addGameInCategory() {
        const categoryToChange = GameCategoriesWithGamesAndLimitsSpec.gameCategories[0];
        const gameCodes: any[] = categoryToChange.items.map(item => item.id);

        GameCategoriesWithGamesAndLimitsSpec.games = GameCategoriesWithGamesAndLimitsSpec.games
            .filter(game => !gameCodes.includes(game));

        categoryToChange.items.shift();

        await getGameCategoryService(GameCategoriesWithGamesAndLimitsSpec.master).update(
            categoryToChange.id, {
                items: categoryToChange.items
            } as any);

        const found = await getGameCategoryService(GameCategoriesWithGamesAndLimitsSpec.brand)
            .findAllWithGamesForLobby(
                true,
                "USD");
        expect(found.length).eq(6);

        const expectedCategoryInfo = { ...categoryToChange };
        const foundCategoryWithGames = found.find(category => category.id === expectedCategoryInfo.id);

        const firstGame = GameCategoriesWithGamesAndLimitsSpec.games
            .find(game => game.code === categoryToChange.items[0].id);
        const secondGame = GameCategoriesWithGamesAndLimitsSpec.games
            .find(game => game.code === categoryToChange.items[1].id);

        expect(foundCategoryWithGames).deep.eq({
            "id": expectedCategoryInfo.id,
            "title": expectedCategoryInfo.title,
            "description": expectedCategoryInfo.description,
            "brandId": expectedCategoryInfo.brandId,
            "status": expectedCategoryInfo.status,
            "type": "general",
            "ordering": 0,
            "isEntityOwner": false,
            "icon": expectedCategoryInfo.icon,
            "translations": expectedCategoryInfo.translations,
            "games": [{
                "code": firstGame.code,
                "title": firstGame.title,
                "type": firstGame.type,
                "defaultInfo": firstGame.defaultInfo,
                "limits": { "USD": firstGame.limits["USD"] },
                "providerTitle": foundCategoryWithGames.games[0].providerTitle,
                "features": {}
            }, {
                "code": secondGame.code,
                "title": secondGame.title,
                "type": secondGame.type,
                "defaultInfo": secondGame.defaultInfo,
                "limits": { "USD": secondGame.limits["USD"] },
                "providerTitle": foundCategoryWithGames.games[1].providerTitle,
                "features": {}
            }]
        });

        expect(GameCategoriesWithGamesAndLimitsSpec.spyFindAll.callCount).eq(1);
        expect(GameCategoriesWithGamesAndLimitsSpec.spyFindCategoryGames.callCount).eq(1);
    }

    @test()
    public async updateGameEntity() {
        const service = await getEntityGameService(GameCategoriesWithGamesAndLimitsSpec.brand);

        await service.update("sw_game_2", { status: "suspended" });

        expect(GameCategoriesWithGamesAndLimitsSpec.spyResetEntityGames.called).to.be.true;
    }
}
