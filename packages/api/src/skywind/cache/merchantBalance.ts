import { BaseEntity, EntityBalance } from "../entities/entity";
import { Cache, ConcurrentCache } from "./cache";
import config from "../config";
import logger from "../utils/logger";
import { sequelize } from "../storage/db";
import { QueryTypes } from "sequelize";

const log = logger("default-ggr-merchant-cache");

const balanceCache: Cache<string, EntityBalance> = new Cache("merchant-balance",
    (key: string, entity: BaseEntity, currency: string) => {
        return entity.fetchBalance(currency);
    }, {
        stdTTL: config.merchantBalance.cache.ttl,
        checkperiod: config.merchantBalance.cache.checkPeriod
    });

export async function findOne(entity: BaseEntity, currency: string): Promise<EntityBalance> {
    const key = `${entity.id}` + ":" + currency;
    return balanceCache.find(key, entity, currency);
}

class MerchantsGGRCacheForDefaultCurrency extends ConcurrentCache<MerchantsDefaultCurrencyInfo> {

    constructor() {
        super(config.merchantBalance.cache.defaultCurrencyTtl, log);
    }

    public async getGGR(brandId: number, currency: string): Promise<number> {
        const allMerchantsInfo = await this.get();
        const info = allMerchantsInfo[brandId];
        if (!info) {
            return 0;
        }
        if (info.defaultCurrency === currency) {
            return info.ggr;
        }
        log.warn(`Wrong default currency ${currency} for merchatn ${brandId}, or default currency for merchant was changed during cache ttl`);
        return 0;
    }

    protected async retrieve(): Promise<MerchantsDefaultCurrencyInfo> {
        const rs: any[] = await sequelize.query(
            "SELECT brand_id, default_currency, revenue FROM bo_aggr_win_bets_by_brand AS aggr INNER JOIN entities AS ent ON (ent.id=aggr.brand_id AND ent.type = 'merchant' AND aggr.currency_code=ent.default_currency)",
            { raw: true, type: QueryTypes.SELECT });
        return rs.reduce((prev: MerchantsDefaultCurrencyInfo, current) => {
            prev[current.brand_id] = { defaultCurrency: current.default_currency, ggr: +current.revenue };
            return prev;
        }, {});
    }
}

const merchantsGGRCacheForDefaultCurrency = new MerchantsGGRCacheForDefaultCurrency();

export async function getMerchantGGRForDefaultCurrency(entityId: number, currency: string): Promise<number> {
    return merchantsGGRCacheForDefaultCurrency.getGGR(entityId, currency);
}

export function resetMerchantsGGRCacheForDefaultCurrencyCache() {
    merchantsGGRCacheForDefaultCurrency.reset();
}

interface MerchantsDefaultCurrencyInfo {
    [brandId: number]: { defaultCurrency: string, ggr: number };
}
