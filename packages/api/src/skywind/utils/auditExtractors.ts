import { BlockedPlayerInfo } from "../entities/player";
import { BaseEntity, EntityDomainInfo, WithBalances } from "../entities/entity";
import { Merchant } from "../entities/merchant";
import { EntityBulkOperationResult, EntityBulkOperationResultInfo } from "../entities/bulk";
import { isBaseEntity } from "../services/bulk/utils";
import { HIDDEN_ACCESS_TOKEN, HIDDEN_PASSWORD } from "./common";

export const hidePassword = (data: { password: string }) => ({ ...data, password: HIDDEN_PASSWORD });

export const hidePasswordAndEditorEntity = (data: any) => {
    const { parameters } = data;
    return {
        ...data,
        password: HIDDEN_PASSWORD,
        parameters: {
            ...parameters,
            editorEntity: {}
        },
        editorEntity: {},
    };
};

export const hideAccessToken = (data: { accessToken: string }) => ({ ...data, accessToken: HIDDEN_ACCESS_TOKEN });

export const getPlayerStatus = (result: BlockedPlayerInfo) => (result.status);
export const getBalances = (result: WithBalances) => ({ balances: result.balances });

export const getBulkOperationResultInfo =
    (results: EntityBulkOperationResult[]): EntityBulkOperationResultInfo[] => results.map(result => {
        if (isBaseEntity(result)) {
            return getEntityDomainInfo(result);
        } else {
            return (result as Merchant).toInfo();
        }
    });

export const getEntityDomainInfo = (entity: BaseEntity): EntityDomainInfo => {
    return {
        key: entity.key,
        dynamicDomainId: entity.dynamicDomainId || undefined,
        staticDomainId: entity.staticDomainId || undefined,
        lobbyDomainId: entity.lobbyDomainId || undefined,
        liveStreamingDomainId: entity.liveStreamingDomainId || undefined,
        ehubDomainId: entity.ehubDomainId || undefined,
        staticDomainPoolId: entity.staticDomainPoolId || undefined,
        dynamicDomainPoolId: entity.dynamicDomainPoolId || undefined,
        environment: entity.environment
    };
};
