export interface JackpotFilterParams {
    siteCode?: string;
    currency?: string;
    jpId?: string;
    widgetCode?: string;
}

export class JackpotFilterBuilder {
    public static readonly SITE_CODE = new RegExp("{siteCode}", "g");
    public static readonly CURRENCY = new RegExp("{currency}", "g");
    public static readonly JP_ID = new RegExp("{jpId}", "g");
    public static readonly WIDGET_CODE = new RegExp("{widgetCode}", "g");

    public static build(template: string, params: JackpotFilterParams): string {
        if (params.siteCode) {
            template = template.replace(JackpotFilterBuilder.SITE_CODE, params.siteCode);
        }
        if (params.currency) {
            template = template.replace(JackpotFilterBuilder.CURRENCY, params.currency);
        }
        if (params.jpId) {
            template = template.replace(JackpotFilterBuilder.JP_ID, params.jpId);
        }
        if (params.widgetCode) {
            template = template.replace(JackpotFilterBuilder.WIDGET_CODE, params.widgetCode);
        }
        return template;
    }
}
