import { Model, ModelStatic, DataTypes, InferAttributes, InferCreationAttributes } from "sequelize";
import { sequelize as db } from "../storage/db";
import { RoundHistory } from "../entities/gameHistory";


export interface AggregateDBRound extends Model<
        InferAttributes<AggregateDBRound>,
        InferCreationAttributes<AggregateDBRound>
    >,
    RoundHistory {
}
const model = db.define<AggregateDBRound, RoundHistory>(
    "bo_aggr_rounds",
    {
        roundId: { field: "round_id", type: DataTypes.BIGINT, allowNull: false, primaryKey: true },
        brandId: { field: "brand_id", type: DataTypes.INTEGER, allowNull: false },
        playerCode: { field: "player_code", type: DataTypes.STRING, allowNull: false },
        gameCode: { field: "game_code", type: DataTypes.STRING, allowNull: false },
        currency: { field: "currency_code", type: DataTypes.CHAR(3), allowNull: false },
        win: { field: "win", type: DataTypes.DECIMAL },
        bet: { field: "bet", type: DataTypes.DECIMAL },
        revenue: { field: "revenue", type: DataTypes.DECIMAL },
        firstTs: { field: "first_ts", type: DataTypes.DATE },
        ts: { field: "last_ts", type: DataTypes.DATE },
        isTest: { field: "is_test", type: DataTypes.BOOLEAN },
        finished: { field: "is_finished", type: DataTypes.BOOLEAN },
        balanceBefore: { field: "start_balance", type: DataTypes.DECIMAL },
        balanceAfter: { field: "end_balance", type: DataTypes.DECIMAL },
        device: { field: "device_code", type: DataTypes.STRING },
    },
    {
        timestamps: false,
        freezeTableName: true
    },
);
export type AggregateRoundModel = ModelStatic<AggregateDBRound>;
export function get(): AggregateRoundModel {
    return model;
}
