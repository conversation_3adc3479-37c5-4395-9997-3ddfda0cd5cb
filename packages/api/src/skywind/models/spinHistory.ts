import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { EventHistory } from "../entities/gameHistory";
import { DBToResultMapper } from "../utils/paginghelper";

export interface SpinHistoryDBInstance extends Model<
        InferAttributes<SpinHistoryDBInstance>,
        InferCreationAttributes<SpinHistoryDBInstance>
    >,
    EventHistory {
    get isPayment(): boolean;
}
export type ISpinHistoryModel = ModelStatic<SpinHistoryDBInstance>;
const model: ISpinHistoryModel = db.define<SpinHistoryDBInstance, EventHistory>(
    "spins_history",
    {
        brandId: { field: "brand_id", type: DataTypes.INTEGER, allowNull: false, primaryKey: true },
        playerCode: { field: "player_code", type: DataTypes.STRING, allowNull: true, primaryKey: true },
        gameId: { field: "game_id", type: DataTypes.STRING, allowNull: false, primaryKey: true },
        gameCode: { field: "game_code", type: DataTypes.STRING, allowNull: false },
        device: { field: "device_id", type: DataTypes.STRING, allowNull: false, primaryKey: true },
        type: { field: "game_type", type: DataTypes.STRING, allowNull: true },
        gameVersion: { field: "game_version", type: DataTypes.STRING, primaryKey: false, allowNull: true },
        endOfRound: { field: "round_ended", type: DataTypes.BOOLEAN, allowNull: false },
        spinNumber: { field: "spin_serial_number", type: DataTypes.INTEGER, primaryKey: true },
        walletTransactionId: { field: "wallet_transaction_id", type: DataTypes.STRING, allowNull: true },
        currency: { field: "currency", type: DataTypes.CHAR(3), allowNull: true },
        win: { field: "win", type: DataTypes.DECIMAL, allowNull: true, defaultValue: 0 },
        bet: { field: "bet", type: DataTypes.DECIMAL, allowNull: true, defaultValue: 0 },
        roundId: { field: "round_id", type: DataTypes.BIGINT, primaryKey: true },
        sessionId: { field: "session_id", type: DataTypes.BIGINT },
        ts: {
            field: "ts",
            type: DataTypes.DATE,
            allowNull: false,
        },
        result: { field: "result", type: DataTypes.JSONB, allowNull: false, },
        test: { field: "test", type: DataTypes.BOOLEAN, allowNull: false, defaultValue: false },
        spinHistoryId: { field: "spin_history_id", type: DataTypes.INTEGER, autoIncrement: true, primaryKey: true },
        balanceBefore: { field: "balance_before", type: DataTypes.DECIMAL, allowNull: true },
        balanceAfter: { field: "balance_after", type: DataTypes.DECIMAL, allowNull: true },
        totalJpWin: { field: "total_jp_win", type: DataTypes.DECIMAL, allowNull: true },
        totalJpContribution: { field: "total_jp_contribution", type: DataTypes.DECIMAL, allowNull: true },
        credit: { field: "credit", type: DataTypes.DECIMAL, allowNull: true },
        debit: { field: "debit", type: DataTypes.DECIMAL, allowNull: true },
        insertedAt: {
            field: "inserted_at",
            type: DataTypes.DATE,
            allowNull: true,
            defaultValue: DataTypes.NOW,
        },
        extraData: { field: "extra_data", type: DataTypes.JSONB },
        freeBetCoin: { field: "free_bet_coin", type: DataTypes.DECIMAL, allowNull: true },
        isHidden: { field: "is_hidden", type: DataTypes.BOOLEAN, allowNull: true },
    },
    {
        timestamps: false,
        freezeTableName: true,
        getterMethods: {
            isPayment(): boolean {
                return this.walletTransactionId !== null;
            }
        }
    },
);
export type SpinHistoryMapper<T> = DBToResultMapper<SpinHistoryDBInstance, T>;

export function getSpinHistoryModel(): ISpinHistoryModel {
    return model;
}
