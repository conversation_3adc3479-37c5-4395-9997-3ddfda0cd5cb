import { QueryTypes, Sequelize } from "sequelize";
import { Models } from "./models";
import { Namespace } from "cls-hooked";
import config from "../config";
import EntityCache from "../cache/entity";
import {
    drop as extBetWinHistoryDrop,
    sync as extBetWinHistorySync,
    truncate as extBetWinHistoryTruncate
} from "@skywind-group/sw-game-provider-ext-game-history";
import { sequelize as db } from "../../skywind/storage/db";
import { readFileSync } from "node:fs";
import { join } from "node:path";

interface Sequence {
    sequence: string;
}

export interface SequenceWithValue extends Sequence {
    value: number;
}

export class DBHelper {
    private sequences: Sequence[];

    public async sync() {
        await Models.sync({ schema: config.db.schema });
        await Models.WinBetModel.sync({ schema: config.db.schema });
        await Models.AggrWinBetModel.sync({ schema: config.db.schema });
        await Models.AggregateRoundModel.sync({ schema: config.db.schema });
        await extBetWinHistorySync();
        await this.createEntityGameStoredProcedure();
        await this.createGameRTPReportProcedure();
        await this.createJackpotReportProcedure();
    }

    public async truncate(db: Sequelize): Promise<void> {
        await Models.GameVersionModel.truncate({ cascade: true });
        await Models.PromotionToPlayerModel.truncate({ cascade: true });
        await Models.WinBetModel.truncate({ cascade: true });
        await Models.AggrWinBetModel.truncate({ cascade: true });
        await Models.AggregateRoundModel.truncate({ cascade: true });
        await Models.PaymentModel.truncate({ cascade: true });
        await Models.CurrencyRatesModel.truncate({ cascade: true });
        await Models.PaymentMethodModel.truncate({ cascade: true });
        await Models.GameLabelModel.truncate({ cascade: true });
        await Models.LabelModel.truncate({ cascade: true });
        await Models.AuditSummaryModel.truncate({ cascade: true });
        await Models.AuditSessionModel.truncate({ cascade: true });
        await Models.AuditModel.truncate({ cascade: true });
        await Models.MerchantPlayerGameGroupModel.truncate({ cascade: true });
        await Models.MerchantModel.truncate({ cascade: true });
        await Models.ProxyModel.truncate({ cascade: true });
        await Models.PlayerPasswordReset.truncate({ cascade: true });
        await Models.PlayerSessionModel.truncate({ cascade: true });
        await Models.PlayerModel.truncate({ cascade: true });
        await Models.GameGroupLimitModel.truncate({ cascade: true });
        await Models.GameGroupModel.truncate({ cascade: true });
        await Models.UserRoleModel.truncate({ cascade: true });
        await Models.RoleModel.truncate({ cascade: true });
        await Models.EntityGameModel.truncate({ cascade: true });
        await Models.GameModel.truncate({ cascade: true });
        await Models.AgentModel.truncate({ cascade: true });
        await Models.SiteTokenModel.truncate({ cascade: true });
        await Models.SiteModel.truncate({ cascade: true });
        await Models.NotificationReceiverModel.truncate({ cascade: true });
        await Models.NotificationsModel.truncate({ cascade: true });
        await Models.UserModel.truncate({ cascade: true });
        await Models.GameProviderModel.truncate({ cascade: true });
        await Models.EntityModel.truncate({ cascade: true });
        await Models.GameCategoryModel.truncate({ cascade: true });
        await Models.PromotionLabelModel.truncate({ cascade: true });
        await Models.PromotionFreebetReward.truncate({ cascade: true });
        await Models.PromotionBonusCoinReward.truncate({ cascade: true });
        await Models.Promotion.truncate({ cascade: true });
        await Models.PromotionToPlayerModel.truncate({ cascade: true });
        await Models.PromotionToPlayerUpdateModel.truncate({ cascade: true });
        await Models.JurisdictionModel.truncate({ cascade: true });
        await Models.EntityJurisdictionModel.truncate({ cascade: true });
        await Models.EntityInfoModel.truncate({ cascade: true });
        await Models.AvailableSiteModel.truncate({ cascade: true });
        await Models.PlayerTerminalModel.truncate({ cascade: true });
        await Models.TerminalModel.truncate({ cascade: true });
        await Models.LobbyModel.truncate({ cascade: true });
        await Models.BrandGGRModel.truncate({ cascade: true });
        await Models.MerchantBlockedPlayerModel.truncate({ cascade: true });
        await Models.MerchantTestPlayerModel.truncate({ cascade: true });
        await Models.EntityPaymentHistoryModel.truncate({ cascade: true });
        await Models.DynamicDomainModel.truncate({ cascade: true });
        await Models.BiReportModel.truncate({ cascade: true });
        await Models.BiSessionModel.truncate({ cascade: true });
        await Models.GameServerSettingsModel.truncate({ cascade: true });
        await Models.PlayerResponsibleGamingSettingsModel.truncate({ cascade: true });
        await Models.PlayerResponsibleGamingModel.truncate({ cascade: true });
        await extBetWinHistoryTruncate();
        await Models.MerchantTypeModel.truncate({ cascade: true });
        await Models.SchemaConfigurationModel.truncate({ cascade: true });
        await Models.SchemaDefinitionModel.truncate({ cascade: true });
        await Models.GameLimitsConfigurationModel.truncate({ cascade: true });
        await Models.SegmentModel.truncate({ cascade: true });
        await Models.CurrencyMultiplierModel.truncate({ cascade: true });
        await Models.DeploymentGroupModel.truncate({ cascade: true });
        await Models.FavoriteGameModel.truncate({ cascade: true });
        await Models.LimitTemplateDBModel.truncate({ cascade: true });
        await Models.GameGroupFilterModel.truncate({ cascade: true });
        await Models.EntityLabelModel.truncate({ cascade: true });
        await Models.LabelGroupModel.truncate({ cascade: true });
        await Models.EntityGameLimitLevelModel.truncate({ cascade: true });
        await Models.LimitLevelModel.truncate({ cascade: true });
        await Models.PlayerInfoModel.truncate({ cascade: true });
        await Models.LoginAuditModel.truncate({ cascade: true });
        await Models.LoginAuditSessionModel.truncate({ cascade: true });
        await Models.RefreshTokenModel.truncate({ cascade: true });

        await Models.StaticDomainPoolItemModel.truncate({ cascade: true });
        await Models.StaticDomainPoolModel.truncate({ cascade: true });
        await Models.StaticDomainModel.truncate({ cascade: true });

        await Models.DynamicDomainPoolItemModel.truncate({ cascade: true });
        await Models.DynamicDomainPoolModel.truncate({ cascade: true });
        await Models.DynamicDomainModel.truncate({ cascade: true });

        await this.truncateSequences(db);
    }

    public async drop(): Promise<void> {
        await Models.GameVersionModel.drop({ cascade: true });
        await Models.WinBetModel.drop({ cascade: true });
        await Models.AggrWinBetModel.drop({ cascade: true });
        await Models.AggregateRoundModel.drop({ cascade: true });
        await Models.PaymentModel.drop({ cascade: true });
        await Models.CurrencyRatesModel.drop({ cascade: true });
        await Models.PaymentMethodModel.drop({ cascade: true });
        await Models.GameLabelModel.drop({ cascade: true });
        await Models.LabelModel.drop({ cascade: true });
        await Models.AuditModel.drop({ cascade: true });
        await Models.AuditSummaryModel.drop({ cascade: true });
        await Models.MerchantPlayerGameGroupModel.drop();
        await Models.AuditSessionModel.drop({ cascade: true });
        await Models.MerchantModel.drop();
        await Models.ProxyModel.drop({ cascade: true });
        await Models.PlayerPasswordReset.drop({ cascade: true });
        await Models.PlayerSessionModel.drop({ cascade: true });
        await Models.PlayerModel.drop({ cascade: true });
        await Models.GameGroupLimitModel.drop({ cascade: true });
        await Models.GameGroupModel.drop({ cascade: true });
        await Models.UserRoleModel.drop({ cascade: true });
        await Models.RoleModel.drop({ cascade: true });
        await Models.EntityGameModel.drop({ cascade: true });
        await Models.GameModel.drop({ cascade: true });
        await Models.AgentModel.drop({ cascade: true });
        await Models.SiteTokenModel.drop({ cascade: true });
        await Models.SiteModel.drop({ cascade: true });
        await Models.NotificationReceiverModel.drop({ cascade: true });
        await Models.NotificationsModel.drop({ cascade: true });
        await Models.UserModel.drop({ cascade: true });
        await Models.GameProviderModel.drop({ cascade: true });
        await Models.EntityModel.drop({ cascade: true });
        await Models.GameCategoryModel.drop({ cascade: true });
        await Models.PromotionLabelModel.drop({ cascade: true });
        await Models.PromotionFreebetReward.drop({ cascade: true });
        await Models.PromotionBonusCoinReward.drop({ cascade: true });
        await Models.Promotion.drop({ cascade: true });
        await Models.PromotionToPlayerModel.drop();
        await Models.PromotionToPlayerUpdateModel.drop();
        await Models.JurisdictionModel.drop({ cascade: true });
        await Models.EntityJurisdictionModel.drop();
        await Models.EntityInfoModel.drop({ cascade: true });
        await Models.AvailableSiteModel.drop();
        await Models.PlayerTerminalModel.drop();
        await Models.TerminalModel.drop();
        await Models.LobbyModel.drop({ cascade: true });
        await Models.BrandGGRModel.drop();
        await Models.MerchantBlockedPlayerModel.drop();
        await Models.MerchantTestPlayerModel.drop();
        await Models.EntityPaymentHistoryModel.drop();
        await Models.PromotionToPlayerModel.drop();
        await Models.GameServerSettingsModel.drop({ cascade: true });
        await Models.PlayerResponsibleGamingSettingsModel.drop();
        await Models.PlayerResponsibleGamingModel.drop();
        await extBetWinHistoryDrop();
        await Models.MerchantTypeModel.drop();
        await Models.SchemaConfigurationModel.drop({ cascade: true });
        await Models.SchemaDefinitionModel.drop({ cascade: true });
        await Models.GameLimitsConfigurationModel.drop({ cascade: true });
        await Models.SegmentModel.drop({ cascade: true });
        await Models.DeploymentGroupModel.drop({ cascade: true });
        await Models.FavoriteGameModel.drop({ cascade: true });
        await Models.LimitTemplateDBModel.drop({ cascade: true });
        await Models.GameRtpHistoryModel.drop({ cascade: true });
        await Models.GameGroupFilterModel.drop({ cascade: true });
        await Models.EntityLabelModel.drop({ cascade: true });
        await Models.LabelGroupModel.drop({ cascade: true });
        await Models.EntityGameLimitLevelModel.drop({ cascade: true });
        await Models.LimitLevelModel.drop({ cascade: true });
        await Models.PlayerInfoModel.drop({ cascade: true });
        await Models.LoginAuditModel.drop({ cascade: true });
        await Models.LoginAuditSessionModel.drop({ cascade: true });
        await Models.RefreshTokenModel.drop({ cascade: true });
        await Models.StaticDomainPoolItemModel.drop({ cascade: true });
        await Models.StaticDomainPoolModel.drop({ cascade: true });
        await Models.StaticDomainModel.drop({ cascade: true });
        await Models.DynamicDomainPoolItemModel.drop({ cascade: true });
        await Models.DynamicDomainPoolModel.drop({ cascade: true });
        await Models.DynamicDomainModel.drop({ cascade: true });
    }

    /**
     * Only for test purposes.
     * @param db
     */
    public async truncateSequences(db: Sequelize): Promise<void> {
        const sequences = await this.getSequences(db);
        const truncateSQL = sequences.reduce((prev, v) =>
            prev + "ALTER SEQUENCE " + v.sequence + " RESTART WITH 1;", "");

        await db.query(truncateSQL, { type: QueryTypes.UPDATE, transaction: null });
    }

    public async getSequenceValues(db: Sequelize): Promise<SequenceWithValue[]> {
        const sequences = await this.getSequences(db);

        const sql = sequences.reduce((prev, current) => {
            let result: string = prev;
            if (prev) {
                result += " UNION ALL ";
            }
            result += "SELECT last_value as value, '" + current.sequence + "' as sequence FROM " + current.sequence;
            return result;
        }, "");

        return db.query(sql, { type: QueryTypes.SELECT, transaction: null });
    }

    private async getSequences(db: Sequelize): Promise<Sequence[]> {
        if (!this.sequences) {
            const getSequencesSQL = "" +
                "SELECT  quote_ident(PGT.schemaname) || '.' || quote_ident(S.relname) as sequence " +
                "FROM pg_class AS S, " +
                "    pg_depend AS D, " +
                "    pg_class AS T, " +
                "    pg_attribute AS C, " +
                "    pg_tables AS PGT " +
                "WHERE S.relkind = 'S'" +
                "   AND S.oid = D.objid" +
                "   AND D.refobjid = T.oid" +
                "   AND D.refobjid = C.attrelid" +
                "   AND D.refobjsubid = C.attnum" +
                "   AND T.relname = PGT.tablename" +
                "   ORDER BY S.relname;";

            this.sequences = await db.query(getSequencesSQL,
                { type: QueryTypes.SELECT });
        }
        return this.sequences;
    }

    public async setSequenceValues(db: Sequelize,
                                   sequences: SequenceWithValue[]): Promise<any> {
        const sql = sequences.reduce((prev, current) =>
            prev + "SELECT setval('" + current.sequence + "'," + current.value + ");", "");

        return db.query(sql, { type: QueryTypes.UPDATE, transaction: null });
    }

    public runInTransaction(ns: Namespace, db: Sequelize) {
        return (func: () => Promise<any>) => {
            return async () => {
                const sequencesValues = await this.getSequenceValues(db);
                let res;
                ns.run(async() => res = db.transaction().then(async (t) => {
                        ns.set("transaction", t);
                        try {
                            await func();
                        } catch (err) {
                            return Promise.reject(err);
                        } finally {
                            try {
                                await t.rollback();
                            } catch (err) {
                                // it's ok
                            }
                            await this.setSequenceValues(db, sequencesValues);
                            EntityCache.reset();
                        }
                    })
                );

                return res;
            };
        };
    }

    private async createEntityGameStoredProcedure(): Promise<void> {
        await this.createProcedureFromFile("resources/sql/entity_games_function.sql");
    }

    private async createGameRTPReportProcedure(): Promise<void> {
        await this.createProcedureFromFile("resources/sql/game_rtp_report_function.sql");
    }

    private async createJackpotReportProcedure(): Promise<void> {
        await this.createProcedureFromFile("resources/sql/entity_jackpots_function.sql");
    }

    private async createProcedureFromFile(filePath: string) {
        const procedureSqlFile = join(process.cwd(), filePath);
        const script = readFileSync(procedureSqlFile, "utf8");
        await db.query(script);
    }
}
