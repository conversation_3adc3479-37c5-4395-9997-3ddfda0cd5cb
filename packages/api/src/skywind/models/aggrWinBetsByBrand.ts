import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { get as getEntityModel } from "./entity";

const EntityModel = getEntityModel();

export interface BrandGGR {
    brandId: number;
    currency: string;
    bet?: number;
    win?: number;
    revenue?: number;
    jackpotWin?: number;
    freeBetWin?: number;
}

export interface BrandGGRDBInstance extends Model<
        InferAttributes<BrandGGRDBInstance>,
        InferCreationAttributes<BrandGGRDBInstance>
    >,
    BrandGGR {
}

const schema = {
    brandId: {
        field: "brand_id",
        type: DataTypes.INTEGER, allowNull: false, primaryKey: true,
        references: {
            model: EntityModel,
            key: "id",
        },
        comment: "Brand ID from \"entities\" table",
    },

    currency: {
        field: "currency_code",
        type: DataTypes.CHAR(3),
        allowNull: false,
        primaryKey: true,
        validate: { isUppercase: true },
        comment: "Currency code",
    },

    bet: {
        field: "bet",
        type: DataTypes.DECIMAL,
        allowNull: false,
        comment: "Staked amount",
    },

    win: {
        field: "win",
        type: DataTypes.DECIMAL,
        allowNull: false,
        comment: "Returned amount",
    },

    revenue: {
        field: "revenue",
        type: DataTypes.DECIMAL,
        allowNull: false,
        comment: "Profit - Loss",
    },

    jackpotWin: {
        field: "jackpot_win",
        type: DataTypes.DECIMAL,
        allowNull: false,
        comment: "Returned amount / JackPot",
    },

    freeBetWin: {
        field: "free_bet_win",
        type: DataTypes.DECIMAL,
        allowNull: false,
        comment: "Returned amount / Free bets",
    },
};

export type IBrandGGRModel = ModelStatic<BrandGGRDBInstance>;
const model: IBrandGGRModel = db.define<BrandGGRDBInstance, BrandGGR>(
    "bo_aggr_win_bets_by_brand",
    schema,
    {
        freezeTableName: true,
        timestamps: false,
        indexes: [
            { fields: ["brand_id"] }
        ]
    }
);

export function getBrandGGRModel() {
    return model;
}
