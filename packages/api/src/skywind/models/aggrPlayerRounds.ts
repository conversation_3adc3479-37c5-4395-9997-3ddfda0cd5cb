import {
    Model,
    ModelStatic,
    DataTypes,
    InferAttributes,
    InferCreationAttributes,
} from "sequelize";
import { sequelize as db } from "../storage/db";
import { get as getEntityModel } from "./entity";

const EntityModel = getEntityModel();

export interface AggrPlayerRounds {
    dateHour: Date;
    dateDay?: Date;
    brandId: number;
    gameCode: string;
    playerCode: string;
    deviceCode: string;
    currency: string;
    exchangeRate?: number;
    rounds?: number;
    events?: number;
    totalBet?: number;
    totalWin?: number;
    totalRevenue?: number;
    startBalance?: number;
    endBalance?: number;
    firstActivity?: Date;
    lastActivity?: Date;
}

export interface AggrPlayerRoundsDBInstance extends Model<
        InferAttributes<AggrPlayerRoundsDBInstance>,
        InferCreationAttributes<AggrPlayerRoundsDBInstance>
    >,
    AggrPlayerRounds {
}

const schema = {
    dateHour: { field: "date_hour", type: DataTypes.DATE, allowNull: false, primaryKey: true },
    dateDay: { field: "date_day", type: DataTypes.DATE, allowNull: false, primaryKey: true },
    brandId: {
        field: "brand_id",
        type: DataTypes.INTEGER,
        allowNull: false,
        primaryKey: true,
        references: {
            model: EntityModel,
            key: "id",
        }
    },
    gameCode: { field: "game_code", type: DataTypes.STRING, allowNull: false, primaryKey: true  },
    playerCode: { field: "player_code", type: DataTypes.STRING, allowNull: false, primaryKey: true  },
    deviceCode: { field: "device_code", type: DataTypes.STRING, allowNull: false },
    currency: {
        field: "currency_code",
        type: DataTypes.CHAR(3),
        allowNull: false,
        primaryKey: true,
        validate: { isUppercase: true }
    },
    exchangeRate: { field: "exchange_rate", type: DataTypes.DECIMAL, allowNull: false, defaultValue: 0 },
    rounds: { field: "rounds_qty", type: DataTypes.INTEGER, allowNull: false },
    events: { field: "events_qty", type: DataTypes.INTEGER },

    totalBet: { field: "total_bet", type: DataTypes.DECIMAL, allowNull: false, defaultValue: 0 },
    totalWin: { field: "total_win", type: DataTypes.DECIMAL, allowNull: false, defaultValue: 0 },
    totalRevenue: { field: "total_revenue", type: DataTypes.DECIMAL, allowNull: false, defaultValue: 0 },

    startBalance: { field: "start_balance", type: DataTypes.DECIMAL },
    endBalance: { field: "end_balance", type: DataTypes.DECIMAL },
    firstActivity: { field: "first_activity", type: DataTypes.DATE },
    lastActivity: { field: "last_activity", type: DataTypes.DATE },
};

export type IAggrPlayerRoundsModel = ModelStatic<AggrPlayerRoundsDBInstance>;
const model: IAggrPlayerRoundsModel = db.define<AggrPlayerRoundsDBInstance, AggrPlayerRounds>(
    "bo_aggr_player_rounds",
    schema,
    {
        freezeTableName: true,
        timestamps: false,
        indexes: [
            { fields: ["brand_id"] }
        ]
    }
);

export function getAggrPlayerRoundsModel() {
    return model;
}
