import { lazy } from "@skywind-group/sw-utils";
import {
    PlayerSegmentValues,
    QueryForSegment,
    Segment,
    SEGMENT_SEARCH_MODE,
    SegmentDBInstance,
    SegmentModel,
    SegmentValues
} from "../../models/segment";
import { CRUDServiceImpl } from "../crudService";
import { ValidationError } from "../../errors";
import { sequelize } from "../../storage/db";
import { QueryTypes, BindOrReplacements } from "sequelize";
import logger from "../../utils/logger";
import { MERCHANT_POP_MOORGATE_TYPE, MERCHANT_POP_MOORGATE_TYPE_EXT_ADAPTER } from "@skywind-group/sw-management-adapters";
import { LIMITS_CONFIGURATION_STATUS } from "./helper";
import { Models } from "../../models/models";

const SegmentModel = Models.SegmentModel;
const segmentCRUDService = lazy(() => new SegmentCRUDService());
export const getSegmentCRUDService = (): SegmentCRUDService => segmentCRUDService.get();

const searchSegmentsService = lazy(() => new SearchSegmentsService());
export const getSearchSegmentsService = ( ): SearchSegmentsService => searchSegmentsService.get();

const fieldsWhereEmptyIsLikeMatch = ["vipLevel"];
const log = logger("segments");

class SegmentCRUDService extends CRUDServiceImpl<SegmentDBInstance, Segment, SegmentModel> {
    private model = SegmentModel;

    public getModel(): SegmentModel {
        return this.model;
    }

    protected validateCreateData(data: Segment): Segment {
        if (data.priority === -1 || data.priority === undefined || data.priority === null) {
            data.priority = 999;
        }

        if (typeof data.priority !== "number" || (data.priority > 999 || data.priority < 1)) {
            throw new ValidationError("Segment priority must be positive integer value");
        }

        if (typeof data.segment !== "object" || Array.isArray(data.segment)) {
            throw new ValidationError("Segment must be an object");
        }

        for (const dimension in data.segment) {
            if (!data.segment.hasOwnProperty(dimension)) {
                continue;
            }

            if (!Array.isArray(data.segment[dimension])) {
                throw new ValidationError(`Segment dimension ${dimension} must be array`);
            }

            data.segment[dimension] = data.segment[dimension].map(value => value.toString());
        }

        return data;
    }

    protected validateUpdateData(data: Segment): Segment {
        if (data.priority) {
            if (typeof data.priority !== "number" || data.priority < 0 || !Number.isInteger(data.priority)) {
                throw new ValidationError("Segment priority must be positive integer value");
            }
        }

        if ("segment" in data) {
            if (typeof data.segment !== "object" || Array.isArray(data.segment)) {
                throw new ValidationError("Segment must be an object");
            }

            for (const dimension in data.segment) {
                if (!data.segment.hasOwnProperty(dimension)) {
                    continue;
                }

                if (!Array.isArray(data.segment[dimension])) {
                    throw new ValidationError(`Segment dimension ${dimension} must be array`);
                }

                data.segment[dimension] = data.segment[dimension].map(value => value.toString());
            }
        }

        return data;
    }
}

class BaseSegmentsSearchService {
    public validateExternalPath(path: number[]) {
        const isInvalidExternalPath = path && path.some(externalId =>
            typeof externalId !== "number" || externalId > Number.MAX_SAFE_INTEGER || externalId < 1);
        if (isInvalidExternalPath) {
            throw new ValidationError("Unexpected external path");
        }
    }

    public validateResellerId(resellerId: number) {
        if (resellerId === undefined) {
            return;
        }

        if (typeof resellerId !== "number" || resellerId > Number.MAX_SAFE_INTEGER || resellerId < 1) {
            throw new ValidationError("Invalid resellerId");
        }
    }

    public mapSegment(segment: any): Segment {
        if (!segment) {
            return;
        }
        return {
            id: segment.id,
            externalId: segment.external_id,
            entityGameId: segment.entity_game_id,
            priority: segment.priority,
            segment: segment.segment,
            status: segment.status,
            externalResellerId: segment.external_reseller_id
        };
    }
}

/*
* Search segment on game launch, segment dimension is string value
*/
abstract class SearchPlayerSegmentService extends BaseSegmentsSearchService {
    private readonly requiredSegmentFields = ["vipLevel", "currency"];

    public abstract segmentExists(entityGameId: number,
                                  values: PlayerSegmentValues,
                                  externalResellerPath: number[],
                                  externalId?: string): Promise<boolean>;

    public abstract searchBestMatch(entityGameId: number,
                                    values: PlayerSegmentValues,
                                    externalResellerPath: number[],
                                    externalId?: string): Promise<Segment>;

    protected async getSegmentId(entityGameId: number,
                                 whereOptions: string,
                                 replacements: BindOrReplacements,
                                 orderBy: string = "\"priority\""): Promise<number> {

        let query = `SELECT id FROM segments where entity_game_id = ${entityGameId}`;

        query += ` and ${whereOptions} ORDER BY ${orderBy} LIMIT 1`;

        const result = await sequelize.query(query, {
            type: QueryTypes.SELECT,
            replacements
        });

        return result && result.length ? result[0]["id"] : null;
    }

    protected async querySegments(entityGameId: number,
                                  whereOptions: string,
                                  replacements: BindOrReplacements,
                                  orderBy: string = "\"priority\"",
                                  withoutLimit: boolean = false,
                                  allStatuses: boolean = false) {

        let query = `SELECT * FROM segments where entity_game_id = ${entityGameId}`;
        if (!allStatuses) {
            query += ` and status = '${LIMITS_CONFIGURATION_STATUS.ACTIVE}'`;
        }

        query += ` and ${whereOptions} ORDER BY ${orderBy}`;

        if (!withoutLimit) {
            query += " LIMIT 1";
        }

        return await sequelize.query(query, {
            type: QueryTypes.SELECT,
            replacements
        });
    }

    protected getQueryForBestMatch(values: PlayerSegmentValues): QueryForSegment {

        const queryValues = [];
        const segmentQuery = {};
        const replacements: any = {};

        for (const field in values) {
            if (values.hasOwnProperty(field) && values[field] !== undefined && values[field] !== null) {
                const value: string = values[field].toString();
                segmentQuery[field] = [value];
                replacements[field] = field;
            }
        }
        replacements.segment = JSON.stringify(segmentQuery);
        queryValues.push("segment @> :segment ::jsonb");

        const keysInValues = Object.keys(values)
            .filter(key => values[key] !== undefined && values[key] !== null).map(key => `:${key}`);

        // segment must contain only received fields
        // TODO: after pg version on CD2 is updated update to (segment - '{${keysInValues}}'::text[]) is NULL
        if (keysInValues.length) {
            queryValues.push(`(segment - ${keysInValues.join(" - ")}) = '{}'`);
        }
        return { queries: queryValues, replacements };
    }

    protected getQueryForPartialPatch(values: PlayerSegmentValues): QueryForSegment {
        const queryValues = [];
        const replacements = {};
        const keysInValues = [];

        for (const field in values) {
            if (values.hasOwnProperty(field) && values[field] !== undefined && values[field] !== null) {
                const keyReplacement = `field${field}`;
                replacements[field] = values[field].toString();
                replacements[keyReplacement] = field;

                const query = [];

                // For some special fields empty string work like partial match
                if (fieldsWhereEmptyIsLikeMatch.includes(field)) {
                    query.push(`(segment ->> :field${field})::jsonb ?| array['', :${field}]`);
                } else {
                    // get exact match
                    query.push(`(segment ->> :field${field})::jsonb ? :${field}`);
                }

                if (!this.requiredSegmentFields.includes(field)) {
                    // match by default value - field doesn't exist in segment
                    query.push(`(segment ->> :field${field}) is null`);
                }

                queryValues.push(`(${query.join(" OR ")})`);
                keysInValues.push(`:${keyReplacement}`);
            }
        }

        // segment must contain only received fields
        // TODO: after pg version on CD2 is updated update to (segment - '{${keysInValues}}'::text[]) is NULL
        if (queryValues.length) {
            queryValues.push(`(segment - ${keysInValues.join(" - ")}) = '{}'`);
        }

        return { queries: queryValues, replacements };
    }

    protected async queryExactBestMatch(entityGameId: number,
                                        values: PlayerSegmentValues,
                                        externalResellerPath: number[]): Promise<Segment> {

        let segments;
        const { queries: queryValues, replacements } = this.getQueryForBestMatch(values);

        // try to find exact match for external reseller
        if (externalResellerPath && externalResellerPath.length) {
            // array of ids starts from parent to child
            externalResellerPath.reverse();

            const whereQuery = `external_reseller_id = ANY(Array[${externalResellerPath}]) ` +
                ` and ${queryValues.join(" and ")}`;
            const orderBy = ` array_position(Array[${externalResellerPath}], external_reseller_id), "priority"`;
            segments = await this.querySegments(entityGameId, whereQuery, replacements, orderBy);
        }

        // try to find exact match where external reseller is not specified
        if (!segments || !segments.length) {
            const whereQuery = `external_reseller_id IS NULL and (${queryValues.join(" AND ")})`;
            segments = await this.querySegments(entityGameId, whereQuery, replacements);
        }

        return segments && super.mapSegment(segments[0]);
    }

    protected async queryPartialBestMatch(entityGameId: number,
                                          values: PlayerSegmentValues,
                                          externalResellerPath: number[]): Promise<Segment> {
        let segments;

        const { queries: queryValues, replacements } = this.getQueryForPartialPatch(values);

        const whereQuery = `(${queryValues.join(" AND ")})`;
        segments = await this.querySegments(entityGameId, whereQuery, replacements, undefined, true);

        segments = segments.map(s => this.mapSegment(s));

        // for partial match need to filter and sort by matchCount
        return this.getClosedSegmentMatch(values, segments, externalResellerPath);
    }

    private getClosedSegmentMatch(values: PlayerSegmentValues,
                                  segments: Segment[],
                                  externalResellerPath: number[] = []): Segment {
        let match;
        for (const resellerId of externalResellerPath) {
            match = this.getClosedSegmentMatchForReseller(values, segments, resellerId);
            if (match) {
                break;
            }
        }

        if (!match) {
            return this.getClosedSegmentMatchForReseller(values, segments);
        }
        return match;
    }

    private getClosedSegmentMatchForReseller(values: PlayerSegmentValues,
                                             segments: Segment[],
                                             resellerId?: number): Segment {
        const result = segments
            .map((segment) => {
                segment.matchCount = 0;

                for (const field in values) {
                    if (values.hasOwnProperty(field) && values[field] !== undefined) {
                        const value: string = values[field].toString();
                        const segmentValues = segment.segment[field];

                        const isResellerMatch = resellerId
                                                ? resellerId === segment.externalResellerId
                                                : !segment.externalResellerId;

                        // if values are not equal - skip it
                        // if resellerId is specified - need to find highest match for reseller
                        // if field doesn't exist in segment - match counter keep's the same but segment is still valid
                        if (!segmentValues) {
                            continue;
                        }

                        if (!isResellerMatch) {
                            segment.matchCount = -1;
                        }

                        // increase matchCount
                        if (!segmentValues.includes(value.toString())) {
                            // for some fields empty value in segment works like partial match
                            // matching counter doesn't increase
                            const isMagicFieldWithEmptyValue = fieldsWhereEmptyIsLikeMatch.includes(field) &&
                                segmentValues.includes("");
                            if (isMagicFieldWithEmptyValue) {
                                continue;
                            } else {
                                segment.matchCount = -1;
                            }
                        } else {
                            segment.matchCount++;
                        }

                        if (segment.matchCount < 0) {
                            break;
                        }
                    }
                }

                return segment;
            })
            .filter(segment => segment.matchCount)
            .sort((segment1: Segment, segment2: Segment) => {
                if (segment1.matchCount === segment2.matchCount) {
                    return segment1.priority - segment2.priority;
                }
                return segment2.matchCount - segment1.matchCount;
            });

        return result[0];
    }
}

class SearchPlayerSegmentAsiaService extends SearchPlayerSegmentService {

    public async searchBestMatch(entityGameId: number,
                                 values: PlayerSegmentValues,
                                 externalResellerPath: number[],
                                 externalId?: string): Promise<Segment> {

        super.validateExternalPath(externalResellerPath);

        // try to find exact match
        // for most cases exact match should exists on game launch
        const exactMatch = await super.queryExactBestMatch(
            entityGameId,
            values,
            externalResellerPath);
        if (exactMatch) {
            log.info(`POP segment matches with exact rule ${exactMatch.id}`);
            return exactMatch;
        }
        /**
         * If there's no exact match need to find the closed match (for resellerId if externalResellerPath is specified)
         * For this, query for all partial matches and filter them in code
         */
        return await super.queryPartialBestMatch(entityGameId, values, externalResellerPath);
    }

    public async segmentExists(entityGameId: number,
                               values: PlayerSegmentValues,
                               externalResellerPath: number[]): Promise<boolean> {

        const { queries: queryValues, replacements } = this.getQueryForPartialPatch(values);

        const segmentId = await this.getSegmentId(entityGameId, `(${queryValues.join(" AND ")})`, replacements);

        return typeof segmentId === "number";
    }
}

class SearchPlayerSegmentEUService extends SearchPlayerSegmentService {
    public async searchBestMatch(entityGameId: number,
                                 values: PlayerSegmentValues,
                                 externalResellerPath: number[],
                                 externalId?: string): Promise<Segment> {

        let whereQuery = `external_id = '${externalId}'`;
        let segments = await this.querySegments(entityGameId, whereQuery, {});

        if (segments.length === 0) {
            whereQuery = "external_id = ''";
            segments = await this.querySegments(entityGameId, whereQuery, {});
        }

        return segments && super.mapSegment(segments[0]);
    }

    public async segmentExists(entityGameId: number,
                               values: PlayerSegmentValues,
                               externalResellerPath: number[],
                               externalId?: string): Promise<boolean> {
        const whereQuery = `(external_id = '${externalId}' OR external_id = '')`;
        const segmentId = await this.getSegmentId(entityGameId, whereQuery, {});

        return typeof segmentId === "number";
    }
}

/*
* Search segment on bet configuration list, segment dimension is array of string
*/
class SearchSegmentsService extends BaseSegmentsSearchService {

    public async search(entityGameId: number,
                        values: SegmentValues,
                        resellerId: number,
                        segmentId: string,
                        mode: SEGMENT_SEARCH_MODE = SEGMENT_SEARCH_MODE.LIKE,
                        allStatuses: boolean = false): Promise<Segment[]> {

        this.validateResellerId(resellerId);

        // try to find exact match for external reseller
        // if mode is exact - all values from received array should be present in stored segment
        return await this.queryExactMatches(
            entityGameId,
            values,
            resellerId,
            segmentId,
            allStatuses,
            mode);
    }

    public async querySegments(entityGameId: number,
                               whereOptions: string,
                               replacements: BindOrReplacements,
                               orderBy: string = "\"priority\"",
                               withoutLimit: boolean = false,
                               allStatuses: boolean = false) {

        let query = `SELECT * FROM segments where entity_game_id = ${entityGameId}`;
        if (!allStatuses) {
            query += ` and status IN('${LIMITS_CONFIGURATION_STATUS.ACTIVE}',` +
                `'${LIMITS_CONFIGURATION_STATUS.INACTIVE}')`;
        }

        query += ` and ${whereOptions} ORDER BY ${orderBy}`;

        if (!withoutLimit) {
            query += " LIMIT 1";
        }

        return await sequelize.query(query, {
            type: QueryTypes.SELECT,
            replacements
        });
    }

    private async queryExactMatches(entityGameId: number,
                                    values: SegmentValues,
                                    resellerId: number,
                                    segmentId: string,
                                    allStatuses?: boolean,
                                    mode: SEGMENT_SEARCH_MODE = SEGMENT_SEARCH_MODE.LIKE): Promise<Segment[]> {

        const queryValues = [];
        const replacements = {};
        const keysInValues = [];
        const operator = mode === SEGMENT_SEARCH_MODE.LIKE ? "?|" : "?&";

        if (resellerId) {
            queryValues.push(`external_reseller_id = ${resellerId}`);
        } else {
            queryValues.push("external_reseller_id IS NULL");
        }

        if (segmentId !== undefined && segmentId !== null) {
            queryValues.push(`external_id = '${segmentId}'`);
        } else {
            queryValues.push("external_id IS NULL");
        }

        for (const field in values) {
            if (values.hasOwnProperty(field) && values[field] !== undefined && values[field] !== null) {
                // on TPI_getBetConfigList value is array
                // need to find segment where intersection of stored and received values exists
                // for exact mode all values from received segment must be present in stored segment
                // for like mode - at least one
                replacements[field] = values[field].map(v => v.toString());
                if (!replacements[field].length) {
                    continue;
                }

                replacements[`field${field}`] = field;

                queryValues.push(`(segment ->> :field${field})::jsonb ${operator} array[:${field}]`);
                keysInValues.push(`:field${field}`);
            }
        }

        if (keysInValues.length) {
            // query to exclude segment which contains more field
            queryValues.push(`(segment - ${keysInValues.join(" - ")}) = '{}'`);
        }

        const segments = await this.querySegments(entityGameId, queryValues.join(" and "), replacements,
            undefined,
            true,
            allStatuses);

        return segments && segments.map(segment => this.mapSegment(segment));
    }
}

const searchPlayerSegmentAsiaService = new SearchPlayerSegmentAsiaService();
const searchPlayerSegmentEUService = new SearchPlayerSegmentEUService();

export const getSearchPlayerSegmentService = (merchantType: string): SearchPlayerSegmentService =>
    merchantType === MERCHANT_POP_MOORGATE_TYPE || merchantType === MERCHANT_POP_MOORGATE_TYPE_EXT_ADAPTER
        ? searchPlayerSegmentAsiaService
        : searchPlayerSegmentEUService;
