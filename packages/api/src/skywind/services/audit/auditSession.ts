import * as Errors from "../../errors";
import {
    AuditSessionDBInstance,
    AuditSessionModel,
    LoginAuditSessionDBInstance,
    LoginAuditSessionModel
} from "../../models/auditSession";
import { CRUDServiceImpl } from "../crudService";
import { lazy } from "@skywind-group/sw-utils";
import { AuditSessionUpdateError } from "../../errors";
import { literal, Op, Transaction, WhereOptions } from "sequelize";
import * as FilterService from "../filter";
import { executeLongQuery, sequelize } from "../../storage/db";
import { BaseEntity } from "../../entities/entity";
import { getChildIds } from "../entity";
import logger from "../../utils/logger";
import { Models } from "../../models/models";
import { AuditSession } from "../../entities/auditSession";

const log = logger("audit");

const AuditSessionModel = Models.AuditSessionModel;
const LoginAuditSessionModel = Models.LoginAuditSessionModel;

export interface AuditSessionService {
    create(auditSession: AuditSession, transaction?: Transaction);
    update(id: string, data: Partial<AuditSession>);
    get(id: string, raiseErrorIfNotFound?: boolean);
    getAll(keyEntity: BaseEntity, query: WhereOptions<any>);
    getQueryParams();
    getSortableKeys();
}

export class AuditSessionServiceImpl
    extends CRUDServiceImpl<AuditSessionDBInstance, AuditSession, AuditSessionModel>
    implements AuditSessionService {
    
    private DEFAULT_SORT_KEY = "startedAt";
    
    public getModel(): AuditSessionModel {
        return AuditSessionModel;
    }
    
    public async create(data: AuditSession, transaction?: Transaction): Promise<AuditSessionDBInstance> {
        try {
            return await super.create(data, transaction);
        } catch (error) {
            throw new Errors.AuditSessionCreationError();
        }
    }

    public async get(id: string, raiseErrorIfNotFound: boolean = true): Promise<AuditSession> {
        try {
            return await super.getInstance(id, raiseErrorIfNotFound);
        } catch (error) {
            return Promise.reject(new Errors.AuditSessionNotFoundError());
        }
    }

    public async getAll(keyEntity: BaseEntity, query: WhereOptions<any> = {}): Promise<AuditSessionDBInstance[]> {
        if (FilterService.valueFromQuery(query, "includeSubEntities") === "true") {
            const childIds = getChildIds(keyEntity);
            query["entityId"] = { [Op.in]: [keyEntity.id, ...childIds].map(v => v.toString()) };
        } else {
            query = { ...query, entityId: keyEntity.id };
        }
        
        const sortBy = FilterService.getSortKey(query, this.getSortableKeys(), this.DEFAULT_SORT_KEY);
        const sortOrder = FilterService.valueFromQuery(query, "sortOrder") || "DESC";
        return await executeLongQuery(sequelize, async (trx) => {
            return AuditSessionModel.findAll({
                where: query,
                offset: FilterService.valueFromQuery(query, "offset"),
                limit: FilterService.valueFromQuery(query, "limit"),
                order: literal(`"${sortBy}" ${sortOrder}`),
                transaction: trx
            });
        });
    }

    public async update(id: string, data: Partial<AuditSession>) {
        try {
            return await super.update(id, data);
        } catch (error) {
            log.error(error, "Update error");
            throw new AuditSessionUpdateError();
        }
    }

    public getQueryParams(): string[] {
        return [
            "id",
            "initiatorName",
            "entityId",
            "startedAt",
            "finishedAt",
            "sortBy",
            "sortOrder",
            "offset",
            "limit",
        ];
    }

    public getSortableKeys(): string[] {
        return [
            "id",
            "initiatorName",
            "entityId",
            "startedAt",
            "finishedAt"
        ];
    }
}

export class LoginAuditSessionServiceImpl
    extends CRUDServiceImpl<LoginAuditSessionDBInstance, AuditSession, LoginAuditSessionModel>
    implements AuditSessionService {

    public getModel(): LoginAuditSessionModel {
        return LoginAuditSessionModel;
    }

    public async create(data: AuditSession, transaction?: Transaction): Promise<LoginAuditSessionDBInstance> {
        try {
            return await super.create(data, transaction);
        } catch (error) {
            log.error(error, "Audit session error");
            throw new Errors.AuditSessionCreationError();
        }
    }

    public get(id: string, raiseErrorIfNotFound: boolean = true): Promise<AuditSession> {
        throw new Error("Not implemented");
    }

    public getAll(keyEntity: BaseEntity, query: WhereOptions<any>) {
        throw new Error("Not implemented");
    }

    public getQueryParams(): string[] {
        throw new Error("Not implemented");
    }

    public getSortableKeys(): string[] {
        throw new Error("Not implemented");
    }
}

const container = lazy<AuditSessionService>(
    () => new AuditSessionServiceImpl());

export function getAuditSessionService(): AuditSessionService {
    return container.get();
}

const loginContainer = lazy<AuditSessionService>(
    () => new LoginAuditSessionServiceImpl());

export function getLoginAuditSessionService(): AuditSessionService {
    return loginContainer.get();
}
