import { Audit, AuditInfo } from "../entities/audit";
import { AuditDBInstance } from "../models/audit";
import { BaseEntity } from "../entities/entity";
import { PagingHelper } from "../utils/paginghelper";
import * as FilterService from "./filter";
import { sequelizeFindToStoredFunctionInput, StoredFunctionInput } from "./filter";
import { literal, Op, WhereOptions, where, cast, col, and } from "sequelize";
import { sequelize } from "../storage/db";
import config from "../config";
import { getChildIds } from "./entity";
import EntityCache from "../cache/entity";
import * as AuditSummaryCache from "../cache/auditSummary";
import { AuditSummary } from "../entities/auditSummary";
import { hidePasswordAndEditorEntity } from "../utils/auditExtractors";
import { Models } from "../models/models";

const AuditModel = Models.AuditModel;
const AuditSlaveModel = Models.AuditSlaveModel;

const auditSummaryMapping = {
    "auditsSummaryEventName": "event_name",
    "auditsSummaryPath": "path",
    "auditsSummaryMethod": "method",
    "auditsSummarySummary": "summary"
};

export const queryParamsKeys = [
    "auditId",
    "includeSubEntities",
    "ts",
    "initiatorType",
    "initiatorName",
    "initiatorServiceName",
    "ip",
    "userAgent",
    "initiatorIssueId",
    "auditsSessionId",
    "auditsSummaryId",
    "sortBy",
    "sortOrder",
    "offset",
    "limit",
    ...Object.keys(auditSummaryMapping)
];

export const paramsMapping = {
    "auditId": "audit_id",
    "ts": "ts",
    "initiatorType": "initiator_type",
    "initiatorName": "initiator_name",
    "initiatorServiceName": "initiator_service_name",
    "ip": "ip::VARCHAR",
    "userAgent": "user_agent",
    "initiatorIssueId": "initiator_issue_id",
    "entityId": "entity_id",
    "auditsSummaryId": "audits_summary_id",
    "auditsSessionId": "audits_session_id",
    "history": "history",
    ...auditSummaryMapping
};

const sortableKeys = ["ts", "ip", "userAgent"];
const DEFAULT_SORT_KEY = "ts";

const AUDITS_QUERY: string = "SELECT * FROM fnc_bo_audits(p_where_filters => '{$whereFilters}', " +
    "p_sort_by => '{$sortBy}', p_limit => $limit , p_offset => $offset)";

export class AuditImpl implements Audit {
    public auditId: number;
    public ts: Date;
    public entityId: number;
    public history: object;
    public initiatorType: string;
    public initiatorName: string;
    public initiatorServiceName: string;
    public initiatorIssueId: string;
    public ip: string;
    public userAgent: string;
    public entity: any;
    public auditsSummaryId: number;
    public auditsSessionId: string;
    public auditsSummary: AuditSummary;

    constructor(item?: AuditDBInstance, entity?: any) {
        if (!item) {
            return;
        }

        this.auditId = item.get("auditId");
        this.ts = item.get("ts");
        this.entityId = item.get("entityId");
        this.history = item.get("history");
        this.initiatorType = item.get("initiatorType");
        this.initiatorName = item.get("initiatorName");
        this.initiatorServiceName = item.get("initiatorServiceName");
        this.initiatorIssueId = item.get("initiatorIssueId");
        this.ip = item.get("ip");
        this.userAgent = item.get("userAgent");
        this.auditsSummaryId = item.get("auditsSummaryId");
        if (entity) {
            this.entity = entity;
        }
        this.auditsSummaryId = item.get("auditsSummaryId");
        this.auditsSessionId = item.get("auditsSessionId");
        this.auditsSummary = item.get("auditsSummary");
    }

    public toInfo(keyEntity?: BaseEntity): AuditInfo {
        let entity: string = this.entity ? this.entity.name : undefined;
        entity = keyEntity && entity ? this.entity.path.replace(keyEntity.path, "") : undefined;

        return {
            auditId: this.auditId,
            ts: this.ts,
            entityId: this.entityId,
            entity: entity,
            initiatorType: this.initiatorType,
            initiatorName: this.initiatorName,
            initiatorServiceName: this.initiatorServiceName || "",
            initiatorIssueId: this.initiatorIssueId || "",
            ip: this.ip,
            userAgent: this.userAgent,
            history: hidePasswordAndEditorEntity(this.history as any),
            auditsSummaryId: this.auditsSummaryId,
            auditsSessionId: this.auditsSessionId,
            auditsSummary: this.auditsSummary ? this.auditsSummary.toInfo() : undefined
        };
    }
}

export async function getActivities(keyEntity: BaseEntity, query: WhereOptions<any> = {}): Promise<Audit[]> {
    if (FilterService.valueFromQuery(query, "includeSubEntities") === "true") {
        const childIds = getChildIds(keyEntity);
        query["entityId"] = { [Op.in]: [keyEntity.id, ...childIds].map(v => v.toString()) };
    } else {
        query = { ...query, entityId: keyEntity.id };
    }

    const sortBy: string = FilterService.getSortKey(query, sortableKeys, DEFAULT_SORT_KEY);
    const sortOrder: string = FilterService.valueFromQuery(query, "sortOrder") || "DESC";

    if (config.gameHistory.useStoredProcedures) {
        const sortKey: string = sortBy === "ip" ? "ip" : paramsMapping[sortBy];
        const funcInput: StoredFunctionInput = sequelizeFindToStoredFunctionInput({ where: query }, paramsMapping);
        const limit = parseInt(FilterService.valueFromQuery(query, "limit"), 10) || FilterService.DEFAULT_LIMIT;
        const offset = parseInt(FilterService.valueFromQuery(query, "offset"), 10) || FilterService.DEFAULT_OFFSET;
        const queryWithParams: string = AUDITS_QUERY
            .replace("$whereFilters", () => funcInput.whereFilters)
            .replace("$sortBy", `${sortKey} ${sortOrder}`)
            .replace("$limit", limit.toString())
            .replace("$offset", offset.toString());

        return PagingHelper.findAsyncAllNative(queryWithParams,
            sequelize,
            AuditSlaveModel,
            limit,
            offset,
            async (item) => {
                const entityId = item.get("entityId");
                const entity: BaseEntity = entityId ? await EntityCache.findById(entityId) : undefined;
                const auditSummaryId = item.get("auditsSummaryId");
                const auditSummary: AuditSummary = await AuditSummaryCache.findById(auditSummaryId);
                item.setDataValue("auditsSummary", auditSummary);
                const instance = new AuditImpl(item, entity);
                return Promise.resolve(instance);
            },
            false,
        );
    }

    let whereIpQuery;
    if (typeof query["ip"] === "object") {
        const ipQuery = { ...query["ip"] };
        delete query["ip"];

        whereIpQuery = where(
            cast(col("ip"), "varchar"),
            ipQuery
        );
    }

    const summaryQuery: WhereOptions<any> = getSummaryQuery(query);
    query = excludeSummaryQuery(query);
    return PagingHelper.findAsyncAndCountAll(AuditModel, {
            where: and(query, whereIpQuery) as any,
            offset: FilterService.valueFromQuery(query, "offset"),
            limit: FilterService.valueFromQuery(query, "limit"),
            order: literal(`"${sortBy}" ${sortOrder}`),
            include: [
                {
                    association: AuditModel.associations.auditsSummary,
                    where: summaryQuery
                }
            ]
        }, async (item) => {
            const entityId = item.get("entityId");
            const entity: BaseEntity = entityId ? await EntityCache.findById(entityId) : undefined;
            const instance = new AuditImpl(item, entity);
            return Promise.resolve(instance);
        }
    );
}

function getSummaryQuery(query: any = {}) {
    const summaryParams = {};
    for (const key of Object.keys(query)) {
        if (key in auditSummaryMapping) {
            summaryParams[auditSummaryMapping[key]] = query[key];
        }
    }
    return summaryParams;
}

function excludeSummaryQuery(query: any = {}) {
    const updatedQuery = {};
    for (const key of Object.keys(query)) {
        if (!(key in auditSummaryMapping)) {
            updatedQuery[key] = query[key];
        }
    }
    return updatedQuery;
}
