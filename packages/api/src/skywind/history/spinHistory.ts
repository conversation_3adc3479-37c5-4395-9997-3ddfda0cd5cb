import { BrandEntity } from "../entities/brand";
import { col, FindOptions, WhereOptions } from "sequelize";
import * as FilterService from "../services/filter";
import { sequelizeFindToStoredFunctionInput, StoredFunctionInput } from "../services/filter";
import { getSpinHistoryModel } from "../models/spinHistory";
import config from "../config";
import { PagingHelper } from "../utils/paginghelper";
import { sequelize as db } from "../storage/db";
import * as MerchantCache from "../cache/merchant";
import { decorateSpinData, findGameHistoryEntries, roundHistorySortableKeys } from "./gameHistory";
import {
    decorateFindOptionsWithLimitOffset,
    decorateQueryWithTs,
    decorateWhereOptionsWithIsPayment
} from "./decorators";
import { SPIN_HISTORY_QUERY, spinHistoryMapping } from "./rawQueries";
import { WALLET_TYPE } from "@skywind-group/sw-wallet-adapter-core";
import { EventHistory, EventHistoryExtendDetails } from "../entities/gameHistory";
import { getEntitySettingsForHistory } from "./gameHistoryV2";
import { CurrencyFormatSettings } from "../entities/settings";

export type GameHistorySpin = Omit<EventHistory, "walletTransactionId"> & {
    trxId?: string;
    freeBetCoin?: number;
    details?: EventHistoryExtendDetails;
};

type DBRow = EventHistory & {
    freeBetCoin?: number;
};

function fromDBRow(
    row: DBRow,
    params: {
        withTrx: boolean,
        hideBalanceBeforeAndAfter: boolean,
        currencyFormatSettings?: CurrencyFormatSettings
    }
) {
    const spin: GameHistorySpin = {
        spinNumber: row.spinNumber,
        type: row.type,
        currency: row.currency,
        bet: +row.bet,
        win: +row.win,
        balanceBefore: row.balanceBefore && !params.hideBalanceBeforeAndAfter ? +row.balanceBefore : undefined,
        balanceAfter: row.balanceAfter && !params.hideBalanceBeforeAndAfter ? +row.balanceAfter : undefined,
        endOfRound: row.endOfRound,
        ts: row.ts,
        test: row.test,
        isPayment: row.isPayment,
        trxId: params.withTrx ? row.walletTransactionId : undefined,
        totalJpContribution: +row.totalJpContribution,
        totalJpWin: +row.totalJpWin,
        credit: +row.credit,
        debit: +row.debit,
        extraData: row.extraData ? row.extraData : undefined,
        gameCode: row.gameCode,
        gameId: row.gameId,
        gameVersion: row.gameVersion,
        freeBetCoin: +row.freeBetCoin ? +row.freeBetCoin : undefined,
        isHidden: row.isHidden
    };
    if (spin.currency && params.currencyFormatSettings) {
        const currencyFormatConfig = params.currencyFormatSettings[spin.currency];
        if (currencyFormatConfig) {
            spin.currencyFormatConfig = currencyFormatConfig;
        }
    }
    return spin;
}

export async function getSpinHistoryByRound(entity: BrandEntity,
                                            roundId: number,
                                            reqQuery: any = {},
                                            playerCode?: string,
                                            withDetails: boolean = false): Promise<GameHistorySpin[]> {

    const whereOptions: WhereOptions<any> = {
        ...FilterService.parseFilter(reqQuery, ["type", "spinNumber", "isHidden"]),
        brandId: entity.id,
        roundId
    };

    if (playerCode) {
        whereOptions.playerCode = playerCode;
    }

    await decorateQueryWithTs(whereOptions as any, reqQuery, findGameHistoryEntries);
    decorateWhereOptionsWithIsPayment(whereOptions, reqQuery);

    const sortBy = FilterService.getSortKey(reqQuery, roundHistorySortableKeys, "spinNumber");
    const sortOrder = FilterService.valueFromQuery(reqQuery, "sortOrder") || "ASC";

    const findOptions: FindOptions<any> = {
        where: whereOptions,
        order: [[col(sortBy), sortOrder]],
    };

    decorateFindOptionsWithLimitOffset(findOptions, reqQuery);

    const spinHistoryModel = getSpinHistoryModel();
    const entitySettings = await getEntitySettingsForHistory(entity);
    const mapper = await getSpinHistoryMapper(entity, {
        withTrx: reqQuery.withTrx && reqQuery.withTrx === "true",
        hideBalanceBeforeAndAfter: entitySettings.hideBalanceBeforeAndAfter,
        currencyFormatSettings: entitySettings.currencyFormatSettings
    });
    let spinsHistory: GameHistorySpin[];

    if (config.gameHistory.useStoredProcedures) {
        spinsHistory = await getSpinHistoryUsingStoredProcedure(findOptions, mapper);
    } else {
        spinsHistory = await PagingHelper.findAndCountAll(spinHistoryModel, findOptions, mapper as any);
    }

    if (withDetails && spinsHistory.length <= config.csvExport.maxSpinsDetails) {
        const params = {
            currencyFormatSettings: entitySettings.currencyFormatSettings
        };
        for (const spin of spinsHistory) {
            spin.details = await decorateSpinData(spin as EventHistory, entity, roundId, spin.spinNumber, params);
        }
    }

    return spinsHistory;
}

async function getSpinHistoryMapper(
    entity: BrandEntity,
    params: {
        withTrx: boolean,
        hideBalanceBeforeAndAfter: boolean,
        currencyFormatSettings?: CurrencyFormatSettings
    }
): Promise<(row: DBRow,
            index?: number,
            arr?: DBRow[]) => GameHistorySpin> {
    const defaultMapper = row => fromDBRow(row, params);

    if (!entity.isMerchant) {
        return defaultMapper;
    }

    const merchant = await MerchantCache.findOne(entity);
    if (merchant.params.walletType === WALLET_TYPE.UPDATE_WIN_ON_ROUND_FINISHED || merchant.params.walletType === WALLET_TYPE.UPDATE_WIN_ON_ROUND_FINISHED_WITH_INTERMEDIATE_PHASES) {
        return (row: any) => {
            const spin = defaultMapper(row);
            if (spin.endOfRound) {
                if (spin.spinNumber === 0) {
                    return spin;
                } else {
                    return {
                        ...spin,
                        balanceBefore: undefined
                    };
                }
            }

            if (spin.spinNumber === 0) {
                if (merchant.params.walletType === WALLET_TYPE.UPDATE_WIN_ON_ROUND_FINISHED_WITH_INTERMEDIATE_PHASES) {
                    return spin;
                }
                return {
                    ...spin,
                    balanceAfter: undefined
                };
            }

            return {
                ...spin,
                balanceAfter: undefined,
                balanceBefore: undefined
            };
        };
    }

    return defaultMapper;
}

export async function getSpinHistoryUsingStoredProcedure(findOptions: FindOptions<any>,
                                                         mapper): Promise<GameHistorySpin[]> {
    const funcInput: StoredFunctionInput = sequelizeFindToStoredFunctionInput(findOptions, spinHistoryMapping);
    const queryWithParams: string = SPIN_HISTORY_QUERY.replace("$whereFilters", () => funcInput.whereFilters)
        .replace("$sortBy", funcInput.sortOrder);
    return PagingHelper.findAndCountAllNative("SELECT * " + queryWithParams,
        "SELECT COUNT(*) " + queryWithParams,
        db,
        getSpinHistoryModel(),
        funcInput.limit,
        funcInput.offset,
        mapper) as any;
}
