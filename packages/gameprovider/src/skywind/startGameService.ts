import { DeferredPayment } from "@skywind-group/sw-deferred-payment";
import { OperatorInfo } from "./operatorInfoRepository";
import { GameSessionInfo } from "./playerSession";
import { ExtraData } from "@skywind-group/sw-wallet-adapter-core";
import { RawWallet } from "@skywind-group/sw-wallet";

export interface StartGameRequest {
    startGameToken: string;
    language?: string;
    deviceId?: string;
    deviceData?: any;
    gameVersion?: string;
}

export interface StartFunGameRequest {
    startGameToken: string | FunStartGameToken;
    language?: string;
    deviceId?: string;
}

export interface FunStartGameToken {
    playerCode: string;
    gameCode: string;
    brandId: number;
    currency: string;
    envId: string;
    gameGroup?: string;
    jCode?: string;
}

export interface StartGameResponse {
    gameToken: string;

    limits?: any;
    jrsdSettings?: any;
    gameSettings?: any;
    [field: string]: any;
}

export interface StartGameResult<RES extends StartGameResponse = StartGameResponse> {
    response: RES;
    operatorInfo?: OperatorInfo;
    sessionInfo?: GameSessionInfo;
    deferredPayments?: DeferredPayment[];
    extraData?: ExtraData;
}

/**
 * Service is responsible for starting the game and getting all parameters that are required for GS
 *
 */
export interface StartGameService {
    /**
     * Start game
     * @param startRequest start game request
     * @param ip ip address that request was sent from
     * @param referrer browser referrer
     * @param wallet raw wallet
     */
    startGame(startRequest: StartGameRequest, ip?: string, referrer?: string, wallet?: RawWallet): Promise<StartGameResult>;

    /**
     * Start fun game
     */
    startFunGame(startRequest: StartFunGameRequest): Promise<StartGameResult>;
}
